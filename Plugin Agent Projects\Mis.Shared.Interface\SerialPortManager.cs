﻿using Mis.Shared.Interface;
using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Data.SQLite;
using System.Drawing;
using System.IO.Ports;
using System.Text;
using WIA;

namespace Mis.Shared.Interface
{
    public class SerialPortManager
    {
        private static readonly SerialPortManager _instance = new SerialPortManager();
        public SerialPort _serialPort;
        public bool IsCaptureImageMode { get; set; } // Property to store the capture mode state
        private HubConnection _hubConnection; // Added HubConnection field
        private bool? _isCaptureImageMode;
        private string _databaseFile;

        public string LastScannedBase64Data { get; set; }
        public event Action<Image> BarcodeImageCaptured; // Define an event
        public event Action<Image> scannerImageCaptured; // Define an event
        public event Action NotificationUpdated;


        private SerialPortManager()
        {

            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");
        }


        #region Barcode 
        public static SerialPortManager Instance => _instance;
        public bool IsPortOpen => _serialPort != null && _serialPort.IsOpen;
        public void StartListening(string comPort = null)
        {
            try
            {
                if (IsPortOpen)
                {
                    // Already open: consider logging or handling this case
                    return;
                }
                // Check if the specified COM port is available
                if (!Array.Exists(SerialPort.GetPortNames(), port => port.Equals(comPort, StringComparison.OrdinalIgnoreCase)))
                {
                    Console.WriteLine($"COM port {comPort} is not available.");
                    return;
                }
                // Force release the port
                ForceReleasePort();
                try
                {
                    // Create a new SerialPort instance
                    _serialPort = new SerialPort(comPort, 9600, Parity.None, 8, StopBits.One);
                    // Subscribe to the DataReceived event
                    _serialPort.DataReceived += SerialPort_DataReceived;

                    // Open the serial port
                    _serialPort.Open();
                    return; // Success
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error opening COM port: {ex.Message}");
                    return;
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
        public async Task<bool> InitializeHubConnection(string hubUrl)
        {
            if (string.IsNullOrWhiteSpace(hubUrl))
                throw new ArgumentException("Hub URL cannot be null or empty", nameof(hubUrl));

            try
            {
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(hubUrl)
                    .Build();

                // Add automatic reconnection
                _hubConnection.Closed += async (error) =>
                {
                    await Task.Delay(new Random().Next(0, 5) * 1000);
                    await _hubConnection.StartAsync();
                };

                await _hubConnection.StartAsync();
                Console.WriteLine("Successfully connected to Hub");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to Hub: {ex.Message}");
                return false;  // Re-throw to allow caller to handle
            }
        }
        private async void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (sender is SerialPort port)
                {
                    // Check if capture image mode is active
                    if (IsCaptureImageMode)
                    {
                        SetCaptureImageMode(false);
                        // If no bytes are available, exit
                        if (port.BytesToRead <= 0)
                            return;

                        // Sleep for a brief moment to ensure data is fully received
                        Thread.Sleep(100);

                        byte[] imgData = new byte[port.BytesToRead];
                        port.Read(imgData, 0, imgData.Length); // Read the data as bytes

                        Console.WriteLine($"Received image data: {imgData.Length} bytes");

                        // Run diagnostics on the received data
                        imgCapture.DiagnoseImageData(imgData);

                        // Auto-detect barcode reader type if not already set
                        if (imgCapture.CurrentReaderType == imgCapture.BarcodeReaderType.Auto)
                        {
                            var detectedType = imgCapture.DetectReaderType(imgData);
                            Console.WriteLine($"Auto-detected barcode reader type: {detectedType}");
                            imgCapture.CurrentReaderType = detectedType;
                        }

                        // Convert the byte array to a Base64 string using the detected/configured reader type
                        LastScannedBase64Data = imgCapture.GetImageAsBase64(imgData, imgCapture.CurrentReaderType);

                        if (LastScannedBase64Data != null)
                        {
                            Console.WriteLine($"✓ Successfully converted image to Base64 using {imgCapture.CurrentReaderType} format");
                            Console.WriteLine($"  Base64 length: {LastScannedBase64Data.Length} characters");

                            // If you need to show the image in a PictureBox, you can still do that
                            Image img = imgCapture.GetbarcodeScannerImage(imgData, imgCapture.CurrentReaderType);
                            if (img != null)
                            {
                                Console.WriteLine($"  Image dimensions: {img.Width}x{img.Height}");
                                BarcodeImageCaptured?.Invoke(img);
                            }
                        }
                        else
                        {
                            Console.WriteLine($"✗ Failed to convert image data using {imgCapture.CurrentReaderType} format");
                            Console.WriteLine("  Trying fallback to auto-detection...");

                            // Try auto-detection as fallback
                            LastScannedBase64Data = imgCapture.GetImageAsBase64(imgData, imgCapture.BarcodeReaderType.Auto);
                            if (LastScannedBase64Data != null)
                            {
                                Console.WriteLine("  ✓ Fallback auto-detection succeeded");
                            }
                            else
                            {
                                Console.WriteLine("  ✗ All conversion attempts failed");
                            }
                        }
                        //SerialPortManager.Instance.StartListening(_comPort);
                        _serialPort.DataReceived += SerialPort_DataReceived;

                    }
                    else
                    {
                        // Barcode reading logic
                        byte[] dataBytes = new byte[port.BytesToRead];
                        port.Read(dataBytes, 0, dataBytes.Length);
                        string data = DecodeWindows1256(dataBytes);
                        if (!string.IsNullOrWhiteSpace(data))
                        {
                            // Check the connection and send data
                            if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                            {
                                Console.WriteLine("data " + data);
                                await _hubConnection.InvokeAsync("SendMessage", data);
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading data: {ex.Message}", "Error");
            }
        }
        public Task CaptureImageAsync(string comPort)
        {
            return CaptureImageAsync(comPort, imgCapture.BarcodeReaderType.Auto);
        }

        public Task CaptureImageAsync(string comPort, imgCapture.BarcodeReaderType readerType)
        {
            // Get the appropriate command for the reader type
            string captureCommand = imgCapture.GetCaptureCommand(readerType);
            string command = "\x16M\r" + captureCommand;

            Console.WriteLine($"Sending capture command for {readerType}: {captureCommand}");

            // Write the command to the serial port
            Write(command);
            return Task.CompletedTask;
        }
        public string DecodeWindows1256(byte[] windows1256Bytes)
        {
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Encoding windows1256 = Encoding.GetEncoding("windows-1256");
                return windows1256.GetString(windows1256Bytes);
            }
            catch (Exception ex)
            {

                throw;
            }

        }
        public void ForceReleasePort()
        {
            if (_serialPort != null)
            {
                try
                {
                    // Unsubscribe from the event
                    _serialPort.DataReceived -= SerialPort_DataReceived;

                    // Close the port if it's open
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                    }

                    // Use reflection to access the BaseStream and force it to close
                    var baseStream = _serialPort.BaseStream;
                    if (baseStream != null)
                    {
                        baseStream.Close();
                        baseStream.Dispose();
                    }

                    // Dispose of the SerialPort
                    _serialPort.Dispose();
                    _serialPort = null; // Set to null to avoid reuse
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error releasing COM port: {ex.Message}");
                }
            }
        }
        public void SetCaptureImageMode(bool mode)
        {
            IsCaptureImageMode = mode; // Set the current capturing mode
        }
        public void Write(string command)
        {
            if (_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Write(command);
            }
            else
            {
                throw new InvalidOperationException("Cannot write to the port because it is not open.");
            }
        }
        public string[] GetAvailableCOMPorts()
        {
            return SerialPort.GetPortNames(); // Return available COM ports
        }
        #endregion

        #region Scanner

        public string ScanWithSelectedScanner(string scannerName)
        {
            if (string.IsNullOrWhiteSpace(scannerName))
            {
                throw new ArgumentException("Scanner name cannot be null or empty.", nameof(scannerName));
            }

            try
            {
                // Create a new WIA Device Manager
                var deviceManager = new DeviceManager();

                // Find the scanner device by name
                DeviceInfo selectedDevice = null;
                foreach (DeviceInfo device in deviceManager.DeviceInfos)
                {
                    if (device.Type == WiaDeviceType.ScannerDeviceType)
                    {
                        // Check if this is the scanner we want based on the scannerName
                        string deviceName = device.Properties["Name"].get_Value().ToString();
                        if (deviceName.Equals(scannerName, StringComparison.OrdinalIgnoreCase))
                        {
                            selectedDevice = device;
                            break;
                        }
                    }
                }

                // Throw an exception if the scanner was not found
                if (selectedDevice == null)
                {
                    throw new Exception($"Scanner '{scannerName}' not found.");
                }

                // Connect to the selected device
                var scanner = selectedDevice.Connect();

                // Select the scanner's flatbed (if applicable)
                var item = scanner.Items[1];

                // Perform the scan
                var commonDialog = new WIA.CommonDialog();
                ImageFile imageFile = (ImageFile)commonDialog.ShowTransfer(item, FormatID.wiaFormatJPEG, false);

                // Save the image to a temporary file
                var tempFilePath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.jpg");
                imageFile.SaveFile(tempFilePath);







                // Convert the image to a base64 string
                byte[] imageBytes = File.ReadAllBytes(tempFilePath);
                string base64Image = Convert.ToBase64String(imageBytes);

                // Clean up the temporary file
                File.Delete(tempFilePath);
                if (base64Image != null)
                {
                    // If you need to show the image in a PictureBox, you can still do that
                    var img = imgCapture.GetDeviceScannerImage(imageBytes);
                    if (img != null)
                    {
                        scannerImageCaptured?.Invoke(img);
                    }
                }
                // Return the base64 representation of the scanned image
                return base64Image;
            }
            catch (Exception ex)
            {
                // Handle errors (e.g., scanner not found, scan failure)
                throw new Exception($"Error during scanning: {ex.Message}", ex);
            }
        }

        #endregion

        #region Notifications

        public async Task<IEnumerable<TransactionDto>> GetAllNotificationsAsync()
        {
            var notifications = new List<TransactionDto>();

            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();
                var query = "SELECT Id, No, HtmlContent, IsPrinted, ReceiveTime FROM Notifications";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        notifications.Add(new TransactionDto
                        {
                            Id = Guid.Parse(reader["Id"].ToString()),
                            No = reader["No"].ToString(),
                            HtmlContent = reader["HtmlContent"].ToString(),
                            IsPrinted = Convert.ToBoolean(reader["IsPrinted"]),
                            ReceiveTime = Convert.ToDateTime(reader["ReceiveTime"])
                        });
                    }
                }
            }

            return notifications;
        }


        public async Task SaveNotificationAsync(TransactionDto input)
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    // Check if the record with the given Id exists
                    var checkQuery = "SELECT COUNT(1) FROM Notifications WHERE Id = @Id";
                    using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

                        if (exists)
                        {
                            // Record exists, so update it
                            var updateQuery = @"
                        UPDATE Notifications 
                        SET No = @No, HtmlContent = @HtmlContent, IsPrinted = @IsPrinted, ReceiveTime = @ReceiveTime 
                        WHERE Id = @Id";

                            using (var updateCommand = new SQLiteCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                                updateCommand.Parameters.AddWithValue("@No", input.No);
                                updateCommand.Parameters.AddWithValue("@HtmlContent", input.HtmlContent);
                                updateCommand.Parameters.AddWithValue("@IsPrinted", input.IsPrinted ? 1 : 0);
                                updateCommand.Parameters.AddWithValue("@ReceiveTime", DateTime.Now);

                                int rowsAffected = await updateCommand.ExecuteNonQueryAsync();
                                Console.WriteLine($"{rowsAffected} row(s) updated.");
                            }
                        }
                        else
                        {
                            // Record doesn't exist, so insert a new one
                            var insertQuery = @"
                        INSERT INTO Notifications (Id, No, HtmlContent, IsPrinted, ReceiveTime)
                        VALUES (@Id, @No, @HtmlContent, @IsPrinted, @ReceiveTime)";

                            using (var insertCommand = new SQLiteCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                                insertCommand.Parameters.AddWithValue("@No", input.No);
                                insertCommand.Parameters.AddWithValue("@HtmlContent", input.HtmlContent);
                                insertCommand.Parameters.AddWithValue("@IsPrinted", input.IsPrinted ? 1 : 0);
                                insertCommand.Parameters.AddWithValue("@ReceiveTime", DateTime.Now);

                                int rowsAffected = await insertCommand.ExecuteNonQueryAsync();
                                Console.WriteLine($"{rowsAffected} row(s) inserted.");
                            }
                        }


                        NotificationUpdated?.Invoke();

                    }

                }
            }
            catch (SQLiteException ex)
            {
                // Handle SQLite-specific exceptions
                Console.WriteLine($"SQLite error saving notification: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                Console.WriteLine($"Error saving notification: {ex.Message}");
            }
        }


        public async Task UpdatePrintStatusAsync(Guid transactionId, bool isPrinted)
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    string updateQuery = "UPDATE Notifications SET IsPrinted = @IsPrinted WHERE Id = @Id";
                    using (var command = new SQLiteCommand(updateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Id", transactionId.ToString());
                        command.Parameters.AddWithValue("@IsPrinted", isPrinted ? 1 : 0);

                        int rowsAffected = await command.ExecuteNonQueryAsync();
                        Console.WriteLine($"{rowsAffected} row(s) updated.");
                    }
                    NotificationUpdated?.Invoke();
                }
            }
            catch (SQLiteException ex)
            {
                // Handle SQLite-specific exceptions
                Console.WriteLine($"SQLite error updating print status: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                Console.WriteLine($"Error updating print status: {ex.Message}");
            }
        }

        #endregion










    }
}