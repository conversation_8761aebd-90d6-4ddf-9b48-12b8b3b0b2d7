using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace Mis.Agent.Barcode.Services
{
    public class TwainImageCaptureService
    {
        private const int TWAIN_32 = 0x0001;
        private const int TWAIN_64 = 0x0002;

        // TWAIN capability constants
        private const int CAP_XFERCOUNT = 0x0001;
        private const int ICAP_XFERMECH = 0x0103;
        private const int TWSX_NATIVE = 0;
        private const int TWSX_FILE = 1;
        private const int TWSX_MEMORY = 2;

        // TWAIN message constants
        private const int MSG_OPENDSM = 0x0301;
        private const int MSG_CLOSEDSM = 0x0302;
        private const int MSG_OPENDS = 0x0401;
        private const int MSG_CLOSEDS = 0x0402;
        private const int MSG_USERSELECT = 0x0403;
        private const int MSG_DISABLEDS = 0x0501;
        private const int MSG_ENABLEDS = 0x0502;
        private const int MSG_ENABLEDSUIONLY = 0x0503;
        private const int MSG_XFERREADY = 0x0601;
        private const int MSG_CLOSEDSREQ = 0x0602;
        private const int MSG_CLOSEDSOK = 0x0603;
        private const int MSG_DEVICEEVENT = 0x0702;

        // TWAIN data group constants
        private const int DG_CONTROL = 0x0001;
        private const int DG_IMAGE = 0x0002;

        // TWAIN data argument type constants
        private const int DAT_PARENT = 0x0004;
        private const int DAT_IDENTITY = 0x0003;
        private const int DAT_USERINTERFACE = 0x0009;
        private const int DAT_EVENT = 0x0012;
        private const int DAT_PENDINGXFERS = 0x0005;
        private const int DAT_SETUPMEMXFER = 0x0006;
        private const int DAT_IMAGEINFO = 0x0101;
        private const int DAT_IMAGENATIVEXFER = 0x0104;

        // TWAIN return codes
        private const int TWRC_SUCCESS = 0;
        private const int TWRC_FAILURE = 1;
        private const int TWRC_CHECKSTATUS = 2;
        private const int TWRC_CANCEL = 3;
        private const int TWRC_DSEVENT = 4;
        private const int TWRC_NOTDSEVENT = 5;
        private const int TWRC_XFERDONE = 6;
        private const int TWRC_ENDOFLIST = 7;
        private const int TWRC_INFONOTSUPPORTED = 8;
        private const int TWRC_DATANOTAVAILABLE = 9;

        [DllImport("twain_32.dll", EntryPoint = "DSM_Entry")]
        private static extern int DSMEntry([In, Out] TwainIdentity origin, [In, Out] TwainIdentity dest, int dg, int dat, int msg, ref IntPtr data);

        [DllImport("twain_32.dll", EntryPoint = "DSM_Entry")]
        private static extern int DSMEntryUserInterface([In, Out] TwainIdentity origin, [In, Out] TwainIdentity dest, int dg, int dat, int msg, ref TwainUserInterface data);

        [DllImport("twain_32.dll", EntryPoint = "DSM_Entry")]
        private static extern int DSMEntryEvent([In, Out] TwainIdentity origin, [In, Out] TwainIdentity dest, int dg, int dat, int msg, ref TwainEvent data);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GlobalLock(IntPtr hMem);

        [DllImport("kernel32.dll")]
        private static extern bool GlobalUnlock(IntPtr hMem);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GlobalFree(IntPtr hMem);

        [StructLayout(LayoutKind.Sequential, Pack = 2)]
        public class TwainIdentity
        {
            public int Id;
            public TwainVersion Version;
            public int ProtocolMajor;
            public int ProtocolMinor;
            public int SupportedGroups;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 34)]
            public string Manufacturer;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 34)]
            public string ProductFamily;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 34)]
            public string ProductName;
        }

        [StructLayout(LayoutKind.Sequential, Pack = 2)]
        public struct TwainVersion
        {
            public int MajorNum;
            public int MinorNum;
            public int Language;
            public int Country;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 34)]
            public string Info;
        }

        [StructLayout(LayoutKind.Sequential, Pack = 2)]
        public struct TwainUserInterface
        {
            public bool ShowUI;
            public bool ModalUI;
            public IntPtr hParent;
        }

        [StructLayout(LayoutKind.Sequential, Pack = 2)]
        public struct TwainEvent
        {
            public IntPtr pEvent;
            public int TWMessage;
        }

        private TwainIdentity appIdentity;
        private TwainIdentity sourceIdentity;
        private IntPtr hwnd;

        public async Task<string> CaptureImageAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    Console.WriteLine("[DEBUG] Initializing TWAIN...");

                    // Initialize TWAIN
                    if (!InitializeTwain())
                    {
                        Console.WriteLine("[DEBUG] Failed to initialize TWAIN");
                        return null;
                    }

                    Console.WriteLine("[DEBUG] TWAIN initialized successfully");

                    // Select and open data source
                    if (!OpenDataSource())
                    {
                        Console.WriteLine("[DEBUG] Failed to open TWAIN data source");
                        return null;
                    }

                    Console.WriteLine("[DEBUG] TWAIN data source opened");

                    // Capture image
                    string base64Image = CaptureImage();

                    // Cleanup
                    CleanupTwain();

                    return base64Image;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DEBUG] TWAIN Exception: {ex.Message}");
                    CleanupTwain();
                    return null;
                }
            });
        }

        private bool InitializeTwain()
        {
            try
            {
                // Create application identity
                appIdentity = new TwainIdentity
                {
                    Id = 0,
                    Version = new TwainVersion
                    {
                        MajorNum = 1,
                        MinorNum = 0,
                        Language = 13, // English
                        Country = 1,   // USA
                        Info = "Mis Agent Barcode Scanner"
                    },
                    ProtocolMajor = 2,
                    ProtocolMinor = 4,
                    SupportedGroups = DG_IMAGE | DG_CONTROL,
                    Manufacturer = "Mis Agent",
                    ProductFamily = "Barcode Scanner",
                    ProductName = "Mis Agent Scanner"
                };

                // Get window handle
                hwnd = GetActiveWindow();

                // Open Data Source Manager
                IntPtr parent = hwnd;
                int result = DSMEntry(appIdentity, null, DG_CONTROL, DAT_PARENT, MSG_OPENDSM, ref parent);

                return result == TWRC_SUCCESS;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] InitializeTwain error: {ex.Message}");
                return false;
            }
        }

        private bool OpenDataSource()
        {
            try
            {
                // Select default data source
                sourceIdentity = new TwainIdentity();
                IntPtr nullPtr = IntPtr.Zero;
                int result = DSMEntry(appIdentity, sourceIdentity, DG_CONTROL, DAT_IDENTITY, MSG_USERSELECT, ref nullPtr);

                if (result != TWRC_SUCCESS)
                {
                    Console.WriteLine("[DEBUG] No TWAIN data source selected");
                    return false;
                }

                // Open the data source
                nullPtr = IntPtr.Zero;
                result = DSMEntry(appIdentity, sourceIdentity, DG_CONTROL, DAT_IDENTITY, MSG_OPENDS, ref nullPtr);

                return result == TWRC_SUCCESS;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] OpenDataSource error: {ex.Message}");
                return false;
            }
        }

        private string CaptureImage()
        {
            try
            {
                // Enable the data source
                TwainUserInterface ui = new TwainUserInterface
                {
                    ShowUI = false,
                    ModalUI = true,
                    hParent = hwnd
                };

                int result = DSMEntryUserInterface(appIdentity, sourceIdentity, DG_CONTROL, DAT_USERINTERFACE, MSG_ENABLEDS, ref ui);

                if (result != TWRC_SUCCESS)
                {
                    Console.WriteLine("[DEBUG] Failed to enable TWAIN data source");
                    return null;
                }

                // Wait for image transfer
                IntPtr imageHandle = IntPtr.Zero;
                result = DSMEntry(appIdentity, sourceIdentity, DG_IMAGE, DAT_IMAGENATIVEXFER, MSG_XFERREADY, ref imageHandle);

                if (result == TWRC_XFERDONE && imageHandle != IntPtr.Zero)
                {
                    // Convert to Base64
                    return ConvertImageToBase64(imageHandle);
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] CaptureImage error: {ex.Message}");
                return null;
            }
        }

        private string ConvertImageToBase64(IntPtr imageHandle)
        {
            try
            {
                IntPtr imagePtr = GlobalLock(imageHandle);
                if (imagePtr == IntPtr.Zero) return null;

                // Create bitmap from memory
                using (var bitmap = new Bitmap(Image.FromHbitmap(imageHandle)))
                {
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        byte[] imageBytes = ms.ToArray();
                        return Convert.ToBase64String(imageBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ConvertImageToBase64 error: {ex.Message}");
                return null;
            }
            finally
            {
                GlobalUnlock(imageHandle);
                GlobalFree(imageHandle);
            }
        }

        private void CleanupTwain()
        {
            try
            {
                if (sourceIdentity != null)
                {
                    // Disable data source
                    TwainUserInterface ui = new TwainUserInterface();
                    DSMEntryUserInterface(appIdentity, sourceIdentity, DG_CONTROL, DAT_USERINTERFACE, MSG_DISABLEDS, ref ui);

                    // Close data source
                    IntPtr nullPtr = IntPtr.Zero;
                    DSMEntry(appIdentity, sourceIdentity, DG_CONTROL, DAT_IDENTITY, MSG_CLOSEDS, ref nullPtr);
                }

                if (appIdentity != null)
                {
                    // Close Data Source Manager
                    IntPtr parent = hwnd;
                    DSMEntry(appIdentity, null, DG_CONTROL, DAT_PARENT, MSG_CLOSEDSM, ref parent);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] CleanupTwain error: {ex.Message}");
            }
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetActiveWindow();
    }
}
