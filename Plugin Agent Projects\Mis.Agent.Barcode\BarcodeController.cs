﻿using Mis.Shared.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeAppService _barcodeAppService;
        //private readonly IPublicBarcodeAppService _publicBarcodeAppService;
        private readonly IScannerAppService _scannerAppService;
        public BarcodeController(IBarcodeAppService barcodeAppService,
            IScannerAppService scannerAppService)
        {
            _barcodeAppService = barcodeAppService;
            _scannerAppService = scannerAppService;
        }

        [HttpGet("ScanAsync")]
        public async Task<IActionResult> ScanAsync()
        {
            try
            {
                var setting = _scannerAppService.GetSetting("IsScanByBarcodeReader");
                bool.TryParse(setting, out bool isScanByBarcodeReader);

                if (isScanByBarcodeReader)
                    return await ScanUsingBarcodeReader();
                else
                    return ScanUsingScannerDevice();
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
            }
            catch (IOException ex)
            {
                return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private async Task<IActionResult> ScanUsingBarcodeReader()
        {
            string comPort = _scannerAppService.GetCOMPort();
            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();

            // Get barcode reader type from configuration (if available)
            var readerTypeSetting = _scannerAppService.GetSetting("BarcodeReaderType");
            var readerType = Mis.Shared.Interface.imgCapture.BarcodeReaderType.Auto; // Default to auto-detect

            if (!string.IsNullOrEmpty(readerTypeSetting) &&
                Enum.TryParse<Mis.Shared.Interface.imgCapture.BarcodeReaderType>(readerTypeSetting, out var parsedType))
            {
                readerType = parsedType;
                Console.WriteLine($"Using configured barcode reader type: {readerType}");
            }
            else
            {
                Console.WriteLine("Using auto-detection for barcode reader type");
            }

            // Set the current reader type
            Mis.Shared.Interface.imgCapture.CurrentReaderType = readerType;

            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

            await SerialPortManager.Instance.CaptureImageAsync(comPort, readerType);
            Thread.Sleep(2000);

            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
            SerialPortManager.Instance.LastScannedBase64Data = null;

            if (string.IsNullOrEmpty(scannedData))
            {
                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            _barcodeAppService.ShowNotification("Barcode Notification", $"Image captured successfully using {Mis.Shared.Interface.imgCapture.CurrentReaderType} format");
            return Ok(new { success = true, data = scannedData, readerType = Mis.Shared.Interface.imgCapture.CurrentReaderType.ToString() });
        }

        [HttpGet("DebugImageCapture")]
        public async Task<IActionResult> DebugImageCapture()
        {
            try
            {
                string comPort = _scannerAppService.GetCOMPort();
                string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();

                Console.WriteLine("=== DEBUG IMAGE CAPTURE ===");

                // Set to auto-detect for debugging
                Mis.Shared.Interface.imgCapture.CurrentReaderType = Mis.Shared.Interface.imgCapture.BarcodeReaderType.Auto;

                _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

                // Capture with detailed logging
                await SerialPortManager.Instance.CaptureImageAsync(comPort);
                Thread.Sleep(3000); // Wait a bit longer for debugging

                string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
                SerialPortManager.Instance.LastScannedBase64Data = null;

                Console.WriteLine("=== DEBUG RESULTS ===");
                Console.WriteLine($"Scan successful: {!string.IsNullOrEmpty(scannedData)}");
                Console.WriteLine($"Final reader type: {Mis.Shared.Interface.imgCapture.CurrentReaderType}");

                if (string.IsNullOrEmpty(scannedData))
                {
                    return Ok(new
                    {
                        success = false,
                        message = "Debug capture failed - check console output for detailed diagnostics",
                        readerType = Mis.Shared.Interface.imgCapture.CurrentReaderType.ToString()
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "Debug capture successful - check console output for detailed diagnostics",
                    data = scannedData,
                    readerType = Mis.Shared.Interface.imgCapture.CurrentReaderType.ToString(),
                    dataLength = scannedData.Length
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Debug capture error: {ex.Message}");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpGet("TestBarcodeReaderTypes")]
        public IActionResult TestBarcodeReaderTypes()
        {
            try
            {
                var supportedTypes = Enum.GetValues<Mis.Shared.Interface.imgCapture.BarcodeReaderType>()
                    .Select(t => new
                    {
                        Type = t.ToString(),
                        Command = t == Mis.Shared.Interface.imgCapture.BarcodeReaderType.Auto ? "Auto-detect" :
                                 Mis.Shared.Interface.imgCapture.GetCaptureCommand(t)
                    }).ToArray();

                return Ok(new
                {
                    success = true,
                    currentType = Mis.Shared.Interface.imgCapture.CurrentReaderType.ToString(),
                    supportedTypes = supportedTypes,
                    message = "Use 'SetBarcodeReaderType' endpoint to change the reader type"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost("SetBarcodeReaderType")]
        public IActionResult SetBarcodeReaderType([FromBody] string readerType)
        {
            try
            {
                if (Enum.TryParse<Mis.Shared.Interface.imgCapture.BarcodeReaderType>(readerType, out var parsedType))
                {
                    Mis.Shared.Interface.imgCapture.CurrentReaderType = parsedType;
                    return Ok(new
                    {
                        success = true,
                        message = $"Barcode reader type set to {parsedType}",
                        currentType = parsedType.ToString()
                    });
                }
                else
                {
                    var validTypes = string.Join(", ", Enum.GetNames<Mis.Shared.Interface.imgCapture.BarcodeReaderType>());
                    return BadRequest(new
                    {
                        success = false,
                        message = $"Invalid reader type. Valid types are: {validTypes}"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpGet("TestSpecificReaderType/{readerType}")]
        public async Task<IActionResult> TestSpecificReaderType(string readerType)
        {
            try
            {
                if (!Enum.TryParse<Mis.Shared.Interface.imgCapture.BarcodeReaderType>(readerType, out var parsedType))
                {
                    var validTypes = string.Join(", ", Enum.GetNames<Mis.Shared.Interface.imgCapture.BarcodeReaderType>());
                    return BadRequest(new { success = false, message = $"Invalid reader type. Valid types are: {validTypes}" });
                }

                string comPort = _scannerAppService.GetCOMPort();
                string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();

                Console.WriteLine($"=== TESTING {readerType.ToUpper()} READER TYPE ===");

                // Set specific reader type
                Mis.Shared.Interface.imgCapture.CurrentReaderType = parsedType;
                var command = Mis.Shared.Interface.imgCapture.GetCaptureCommand(parsedType);
                Console.WriteLine($"Using command: {command}");

                _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

                // Capture with specific type
                await SerialPortManager.Instance.CaptureImageAsync(comPort, parsedType);
                Thread.Sleep(3000);

                string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
                SerialPortManager.Instance.LastScannedBase64Data = null;

                Console.WriteLine($"=== {readerType.ToUpper()} TEST RESULTS ===");
                Console.WriteLine($"Success: {!string.IsNullOrEmpty(scannedData)}");

                if (string.IsNullOrEmpty(scannedData))
                {
                    return Ok(new
                    {
                        success = false,
                        message = $"Test failed for {readerType} - check console output for details",
                        readerType = readerType,
                        command = command
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = $"Test successful for {readerType}",
                    data = scannedData,
                    readerType = readerType,
                    command = command,
                    dataLength = scannedData.Length
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test error for {readerType}: {ex.Message}");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private IActionResult ScanUsingScannerDevice()
        {
            string selectedScanner = _scannerAppService.GetSetting("Scanner");

            if (string.IsNullOrEmpty(selectedScanner))
            {
                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
            }

            var scanners = _scannerAppService.GetAvailableScanners();
            if (scanners == null || !scanners.Any())
            {
                return BadRequest(new { success = false, message = "No scanners found." });
            }

            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

            if (string.IsNullOrEmpty(scannedData))
            {
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            return Ok(new { success = true, data = scannedData });
        }


        //[HttpGet("ScanAsync")]
        //public async Task<IActionResult> ScanAsync()
        //{
        //    try
        //    {
        //        //Retrieve the value of IsScanByBarcodeReader from the configuration(appsettings.json)
        //        bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));
        //        if (isScanByBarcodeReader)
        //        {
        //            //Barcode Reader logic
        //            string comPort = _scannerAppService.GetCOMPort();
        //            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
        //            // Initialize the barcode service
        //            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);
        //            // Capture the image
        //           await SerialPortManager.Instance.CaptureImageAsync(comPort);
        //             Thread.Sleep(2000);

        //            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;

        //            SerialPortManager.Instance.LastScannedBase64Data = null;
        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        else
        //        {
        //            //Non - barcode scanner logic: Get the scanner name from appsettings

        //            string selectedScanner = _scannerAppService.GetSetting("Scanner");

        //            if (string.IsNullOrEmpty(selectedScanner))
        //            {
        //                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
        //            }

        //            //Scanner logic
        //            var scanners = _scannerAppService.GetAvailableScanners();
        //            if (scanners == null || !scanners.Any())
        //            {
        //                return BadRequest(new { success = false, message = "No scanners found." });
        //            }

        //            //Use the selected scanner to perform scanning
        //            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        return Ok(new { success = true });
        //    }
        //    catch (UnauthorizedAccessException ex)
        //    {
        //        return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
        //    }
        //    catch (IOException ex)
        //    {
        //        return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = ex.Message });
        //    }
        //}
    }
}
