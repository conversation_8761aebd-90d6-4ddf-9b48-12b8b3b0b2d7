2025-07-15 16:45:26.296 +03:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-07-15 16:45:26.727 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:45:26.946 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:45:26.954 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:45:26.968 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:45:26.976 +03:00 [INF] Hosting environment: Production
2025-07-15 16:45:26.979 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:46:26.270 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:46:26.398 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:46:26.411 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:46:26.424 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:46:26.427 +03:00 [INF] Initializing hub connection...
2025-07-15 16:46:57.161 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:46:57.254 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:46:57.268 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:46:57.285 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:46:57.289 +03:00 [INF] Initializing hub connection...
2025-07-15 16:46:59.359 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:46:59.445 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:46:59.450 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:46:59.455 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:46:59.457 +03:00 [INF] Hosting environment: Production
2025-07-15 16:46:59.459 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:46:59.768 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:46:59.809 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:46:59.833 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:46:59.837 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 74.3389ms
2025-07-15 16:47:01.980 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PRNaehZfw1a9-OcCzQZgAg - null null
2025-07-15 16:47:01.994 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:02.066 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:47:02.147 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:47:08.221 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/ - null null
2025-07-15 16:47:08.256 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/ - 301 0 null 34.6323ms
2025-07-15 16:47:08.267 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-15 16:47:08.311 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 43.5083ms
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-15 16:47:08.376 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-15 16:47:08.380 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 32.4567ms
2025-07-15 16:47:08.382 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 35.0573ms
2025-07-15 16:47:08.449 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-15 16:47:08.449 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-15 16:47:08.454 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 106.2681ms
2025-07-15 16:47:08.454 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 106.6712ms
2025-07-15 16:47:08.458 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-15 16:47:08.466 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 119.0447ms
2025-07-15 16:47:08.688 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-15 16:47:08.804 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 116.3642ms
2025-07-15 16:47:16.098 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:47:16.110 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:47:16.128 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:47:16.545 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:47:33.160 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:47:33.166 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:47:33.170 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:47:33.184 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:47:33.190 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:47:33.193 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:47:33.195 +03:00 [INF] Initializing hub connection...
2025-07-15 16:47:35.237 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:47:35.253 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:47:35.256 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:47:35.281 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.799ms
2025-07-15 16:47:37.316 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=7cWUrw5aRYYpgV809o08cA - null null
2025-07-15 16:47:37.323 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:37.343 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:47:47.045 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 13854.5932ms.
2025-07-15 16:47:47.055 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:47:47.076 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) in 13900.9512ms
2025-07-15 16:47:47.078 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:47:47.079 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 400 null application/json; charset=utf-8 13918.6837ms
2025-07-15 16:47:48.310 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:47:48.317 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:47:48.319 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:47:48.321 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.1961ms
2025-07-15 16:47:50.373 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=AoAGzKeMZ_kHrgjKiyTYRQ - null null
2025-07-15 16:47:50.380 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:51.092 +03:00 [INF] Connection id "0HNE3KO96IF8H", Request id "0HNE3KO96IF8H:00000001": the application aborted the connection.
2025-07-15 16:47:51.101 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 16:47:51.103 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=7cWUrw5aRYYpgV809o08cA - 101 null null 13787.1123ms
2025-07-15 16:48:24.459 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:48:24.464 +03:00 [INF] COM ports populated.
2025-07-15 16:48:24.490 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 16:48:24.491 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:49:29.882 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:49:30.013 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:49:30.028 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:49:30.043 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:49:30.047 +03:00 [INF] Initializing hub connection...
2025-07-15 16:49:32.861 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:49:32.945 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:49:32.950 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:49:32.957 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:49:32.961 +03:00 [INF] Hosting environment: Production
2025-07-15 16:49:32.964 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:49:33.217 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:49:33.273 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:49:33.302 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:49:33.308 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 99.8286ms
2025-07-15 16:49:35.469 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=nQUU_wKbBUOehEcN4oa-mg - null null
2025-07-15 16:49:35.483 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:49:35.622 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:49:35.770 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:51:00.727 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:51:00.840 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:51:00.853 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:51:00.868 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:51:00.871 +03:00 [INF] Initializing hub connection...
2025-07-15 16:51:03.371 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:51:03.479 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:51:03.484 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:51:03.491 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:51:03.494 +03:00 [INF] Hosting environment: Production
2025-07-15 16:51:03.496 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:51:03.953 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:51:04.002 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:51:04.034 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:51:04.040 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 94.6402ms
2025-07-15 16:51:06.233 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=VQN11CdkINRC9fkHHJWUng - null null
2025-07-15 16:51:06.242 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:51:06.355 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:51:06.453 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:51:59.986 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:52:00.089 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:52:00.103 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:52:00.115 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:52:00.119 +03:00 [INF] Initializing hub connection...
2025-07-15 16:52:03.001 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:52:03.097 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:52:03.106 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:52:03.112 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:52:03.114 +03:00 [INF] Hosting environment: Production
2025-07-15 16:52:03.116 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:52:03.350 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:52:03.391 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:52:03.420 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:52:03.428 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 86.0135ms
2025-07-15 16:52:05.613 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=U8_0vhGOeTivgB2bWrHUig - null null
2025-07-15 16:52:05.620 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:52:05.738 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:52:05.826 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:52:11.859 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:11.868 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:11.872 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 13.6878ms
2025-07-15 16:52:11.877 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:11.884 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:11.886 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:52:11.907 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:52:12.310 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:52:12.316 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:52:12.318 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:52:12.319 +03:00 [INF] Initializing hub connection...
2025-07-15 16:52:14.363 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:52:14.371 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:52:14.373 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:52:14.405 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.4437ms
2025-07-15 16:52:16.424 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ydqMUPr9C2XfpPy7X8GPsg - null null
2025-07-15 16:52:16.432 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:52:42.248 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:42.256 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:42.258 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.8158ms
2025-07-15 16:53:02.265 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:02.271 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:02.272 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:02.274 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:02.291 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:53:02.294 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:53:02.297 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:53:02.298 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:04.353 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:04.359 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:04.360 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:04.386 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.576ms
2025-07-15 16:53:06.413 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=fvo4PWehleNTkIlWll9zkQ - null null
2025-07-15 16:53:06.418 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:06.426 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:53:14.369 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 12070.1294ms.
2025-07-15 16:53:14.378 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:53:14.390 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) in 12110.2709ms
2025-07-15 16:53:14.392 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:14.395 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 12129.636ms
2025-07-15 16:53:15.414 +03:00 [INF] Connection id "0HNE3KR3LJH8L", Request id "0HNE3KR3LJH8L:00000001": the application aborted the connection.
2025-07-15 16:53:15.425 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 16:53:15.428 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=fvo4PWehleNTkIlWll9zkQ - 101 null null 9014.9405ms
2025-07-15 16:53:15.598 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:15.605 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:15.608 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:15.632 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.0004ms
2025-07-15 16:53:16.318 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:16.321 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:16.323 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3432ms
2025-07-15 16:53:17.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mzXV-oamxxASKLlSc4IUgg - null null
2025-07-15 16:53:17.659 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:36.328 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:36.335 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:36.336 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:36.338 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:36.361 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:53:36.364 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:53:36.367 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:53:36.368 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:38.407 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:38.410 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:38.412 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:38.431 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 24.3196ms
2025-07-15 16:53:46.627 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:53:46.750 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:53:46.762 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:53:46.777 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:53:46.780 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:49.033 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:53:49.196 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:53:49.202 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:53:49.211 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:53:49.215 +03:00 [INF] Hosting environment: Production
2025-07-15 16:53:49.219 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:53:49.353 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:49.414 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:49.447 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:49.452 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 110.4972ms
2025-07-15 16:53:51.631 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ic3KJ2QBNvxQMTx6H7Iqvw - null null
2025-07-15 16:53:51.646 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:51.913 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:53:52.084 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:53:57.690 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageWIA - null null
2025-07-15 16:53:57.697 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageWIA (Mis.Agent.Barcode)'
2025-07-15 16:53:57.717 +03:00 [INF] Route matched with {action = "CaptureImageWIA", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageWIA() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:58.158 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageWIA (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:49.450 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:54:49.462 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:49.467 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:54:49.478 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:49.502 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 17.1316ms.
2025-07-15 16:54:49.512 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:49.541 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) in 68.7593ms
2025-07-15 16:54:49.545 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:49.549 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - 400 null application/json; charset=utf-8 99.3043ms
2025-07-15 16:54:52.870 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:54:52.874 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:52.876 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:54:52.887 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:52.902 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 12.7211ms.
2025-07-15 16:54:52.904 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:52.907 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) in 27.2557ms
2025-07-15 16:54:52.909 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:52.911 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - 400 null application/json; charset=utf-8 41.1392ms
2025-07-15 16:55:10.082 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:55:10.110 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 404 0 null 27.6523ms
2025-07-15 16:55:10.118 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader, Response status code: 404
2025-07-15 16:55:10.937 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:55:10.943 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 404 0 null 6.7859ms
2025-07-15 16:55:10.950 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader, Response status code: 404
2025-07-15 16:57:01.478 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:57:01.621 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 16:57:01.641 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 16:57:01.657 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:57:01.663 +03:00 [INF] Initializing hub connection...
2025-07-15 16:57:05.139 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:57:05.362 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:57:05.371 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:57:05.385 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:57:05.390 +03:00 [INF] Hosting environment: Production
2025-07-15 16:57:05.397 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:57:05.908 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:57:06.095 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:57:06.182 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:57:06.192 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 311.0167ms
2025-07-15 16:57:08.610 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eYZ3NATWMzCuyyLNk5HL0w - null null
2025-07-15 16:57:08.627 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:57:08.782 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 16:57:08.867 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:57:21.542 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:57:21.550 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:57:21.554 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.479ms
2025-07-15 16:57:21.559 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:57:21.563 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:57:21.565 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:57:21.593 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:57:22.091 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:57:28.814 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 6709.8909ms.
2025-07-15 16:57:28.858 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:57:28.876 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:57:28.878 +03:00 [INF] COM ports populated.
2025-07-15 16:57:28.885 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 7283.3615ms
2025-07-15 16:57:28.887 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:57:28.889 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 7329.8882ms
2025-07-15 16:57:30.019 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 16:57:30.020 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:57:38.232 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:57:38.275 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:57:38.281 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:57:38.285 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:57:38.287 +03:00 [INF] Initializing hub connection...
2025-07-15 16:57:39.325 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:57:39.370 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:57:39.374 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:57:39.380 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:57:39.383 +03:00 [INF] Hosting environment: Production
2025-07-15 16:57:39.385 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:57:40.438 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:57:40.473 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:57:40.491 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:57:40.496 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.3308ms
2025-07-15 16:57:42.543 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-B4EU-wEy21wMoI_5Ee1Iw - null null
2025-07-15 16:57:42.551 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:57:42.591 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:57:42.644 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:58:45.815 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:58:45.856 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:58:45.859 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 43.2838ms
2025-07-15 16:58:45.863 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:58:45.867 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:58:45.868 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:58:45.875 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:58:46.256 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:58:46.257 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:58:46.258 +03:00 [INF] Initializing hub connection...
2025-07-15 16:58:48.286 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:58:48.291 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:58:48.293 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:58:48.296 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 9.989ms
2025-07-15 16:58:48.300 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:58:48.316 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2437.2854ms
2025-07-15 16:58:48.319 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:58:48.321 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2458.1757ms
2025-07-15 16:58:50.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=fCT9LzS1QeK2VyV68w1nEA - null null
2025-07-15 16:58:50.335 +03:00 [INF] Executing endpoint '/chatHub'
