﻿using System;
using System.Drawing;
using System.Text;
using System.IO;
using System.Collections.Generic;

namespace Mis.Shared.Interface
{
    public static class imgCapture
    {
        // Honeywell 1950 series commands (current working commands)
        const string hwImgsnpCmd_1950 = "IMGSNP2G1B1L";//أمر التصوير
        const string hwImgshpCmd_1950 = "IMGSHP2P0L843R639B0T0M8D1S6F";//أمر جلب الصورة من القارئ - يعمل على 1950

        // Honeywell 1900 series commands (different format for older model)
        const string hwImgsnpCmd_1900 = "IMGSNP2G1B1L";//أمر التصوير
        const string hwImgshpCmd_1900 = "IMGSHP8F75K26U";//أمر جلب الصورة من القارئ للموديل 1900

        // Default commands (for backward compatibility - using 1950 commands as default since they work)
        public readonly static string hwPictureCmd = string.Format("{0};{1}.", hwImgsnpCmd_1950, hwImgshpCmd_1950);//الأوامر الافتراضية - تعمل على 1950
        public readonly static string hwPictureCmd_1900 = string.Format("{0};{1}.", hwImgsnpCmd_1900, hwImgshpCmd_1900);//أمر للموديل 1900
        public readonly static string hwPictureCmd_1950 = string.Format("{0};{1}.", hwImgsnpCmd_1950, hwImgshpCmd_1950);//أمر للموديل 1950

        private readonly static int honeyWellPictureCmdLength = hwPictureCmd.Length; //طول الأمر الافتراضي (1950)
        private readonly static int honeyWellPictureCmdLength_1900 = hwPictureCmd_1900.Length; //طول الأمر للموديل 1900
        private readonly static int honeyWellPictureCmdLength_1950 = hwPictureCmd_1950.Length; //طول الأمر للموديل 1950

        // Barcode reader types
        public enum BarcodeReaderType
        {
            Honeywell1900,
            Honeywell1950,
            Generic,
            Auto // Auto-detect
        }

        // Current barcode reader type (can be set dynamically)
        public static BarcodeReaderType CurrentReaderType { get; set; } = BarcodeReaderType.Auto;
        public static Image GetDeviceScannerImage(byte[] imageArray)
        {
            // Check if imageArray is null or empty
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null; // Not enough data for the image
            }

            // Attempt to create an image directly from the byte array
            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    Image img = Image.FromStream(ms);
                    Console.WriteLine("Image successfully created from byte array.");
                    return img; // Return the image if successful
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to image: {ex.Message}");
                return null; // Return null if there's an error
            }
        }




        public static Image GetbarcodeScannerImage(byte[] imageArray, BarcodeReaderType readerType)
        {
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null;
            }

            // Try different parsing methods based on reader type
            if (readerType == BarcodeReaderType.Auto)
            {
                // Try all known formats - start with 1950 since current commands work with it
                var image = TryParseHoneywell1950Format(imageArray);
                if (image != null) return image;

                image = TryParseHoneywell1900Format(imageArray);
                if (image != null) return image;

                image = TryParseGenericFormat(imageArray);
                if (image != null) return image;

                Console.WriteLine("Failed to parse image with any known format");
                return null;
            }
            else if (readerType == BarcodeReaderType.Honeywell1900)
            {
                return TryParseHoneywell1900Format(imageArray);
            }
            else if (readerType == BarcodeReaderType.Honeywell1950)
            {
                return TryParseHoneywell1950Format(imageArray);
            }
            else
            {
                return TryParseGenericFormat(imageArray);
            }
        }

        private static Image TryParseHoneywell1900Format(byte[] imageArray)
        {
            try
            {
                Console.WriteLine("Trying Honeywell 1900 format parsing...");
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) startIndex = 0;

                // More flexible parsing - try multiple approaches
                if (startIndex < imageArray.Length)
                {
                    // Try with 1900-specific command length
                    int remainingBytes = imageArray.Length - startIndex;
                    Console.WriteLine($"  1900 format: remaining bytes = {remainingBytes}, required = {honeyWellPictureCmdLength_1900 + 2}");

                    if (remainingBytes > honeyWellPictureCmdLength_1900 + 2)
                    {
                        try
                        {
                            using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength_1900 - 2))
                            {
                                var img = Image.FromStream(ms);
                                Console.WriteLine("  ✓ 1900 format succeeded with specific command length");
                                return img;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"  1900 specific length failed: {ex.Message}");
                        }
                    }

                    // Try without command length subtraction
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                        {
                            var img = Image.FromStream(ms);
                            Console.WriteLine("  ✓ 1900 format succeeded without command length subtraction");
                            return img;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  1900 without subtraction failed: {ex.Message}");
                    }
                }

                Console.WriteLine("  ✗ 1900 format failed");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing Honeywell 1900 format: {ex.Message}");
                return null;
            }
        }

        private static Image TryParseHoneywell1950Format(byte[] imageArray)
        {
            try
            {
                Console.WriteLine("Trying Honeywell 1950 format parsing...");
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) startIndex = 0;

                // More flexible parsing - try multiple approaches
                if (startIndex < imageArray.Length)
                {
                    // Try with 1950-specific command length
                    int remainingBytes = imageArray.Length - startIndex;
                    Console.WriteLine($"  1950 format: remaining bytes = {remainingBytes}, required = {honeyWellPictureCmdLength_1950 + 2}");

                    if (remainingBytes > honeyWellPictureCmdLength_1950 + 2)
                    {
                        try
                        {
                            using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength_1950 - 2))
                            {
                                var img = Image.FromStream(ms);
                                Console.WriteLine("  ✓ 1950 format succeeded with specific command length");
                                return img;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"  1950 specific length failed: {ex.Message}");
                        }
                    }

                    // Try without command length subtraction
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                        {
                            var img = Image.FromStream(ms);
                            Console.WriteLine("  ✓ 1950 format succeeded without command length subtraction");
                            return img;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  1950 without subtraction failed: {ex.Message}");
                    }
                }

                Console.WriteLine("  ✗ 1950 format failed");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing Honeywell 1950 format: {ex.Message}");
                return null;
            }
        }

        private static Image TryParseGenericFormat(byte[] imageArray)
        {
            try
            {
                // Try to parse as a direct image without command overhead
                try
                {
                    using (MemoryStream ms = new MemoryStream(imageArray))
                    {
                        return Image.FromStream(ms);
                    }
                }
                catch { }

                // Try with image start index
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex >= 0 && startIndex < imageArray.Length)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                        {
                            return Image.FromStream(ms);
                        }
                    }
                    catch { }
                }

                // Try to find JPEG/PNG headers
                var jpegStart = FindJpegStart(imageArray);
                if (jpegStart >= 0)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, jpegStart, imageArray.Length - jpegStart))
                        {
                            return Image.FromStream(ms);
                        }
                    }
                    catch { }
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing generic format: {ex.Message}");
                return null;
            }
        }

        private static int FindImageStartIndex(byte[] imageArray)
        {
            for (int i = 0; i < imageArray.Length; i++)
            {
                if (imageArray[i] == 0x1d) // Image data starts from byte value 0x1D
                {
                    return i + 1; // Increment to start from the next byte
                }
            }
            return 0; // If no start marker found, start from beginning
        }

   

        public static string GetImageAsBase64(byte[] imageArray, BarcodeReaderType readerType)
        {
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null;
            }

            // Try different parsing methods based on reader type
            if (readerType == BarcodeReaderType.Auto)
            {
                // Try all known formats - start with 1950 since current commands work with it
                var base64 = TryConvertToBase64Honeywell1950(imageArray);
                if (base64 != null) return base64;

                base64 = TryConvertToBase64Honeywell1900(imageArray);
                if (base64 != null) return base64;

                base64 = TryConvertToBase64Generic(imageArray);
                if (base64 != null) return base64;

                Console.WriteLine("Failed to convert image to Base64 with any known format");
                return null;
            }
            else if (readerType == BarcodeReaderType.Honeywell1900)
            {
                return TryConvertToBase64Honeywell1900(imageArray);
            }
            else if (readerType == BarcodeReaderType.Honeywell1950)
            {
                return TryConvertToBase64Honeywell1950(imageArray);
            }
            else
            {
                return TryConvertToBase64Generic(imageArray);
            }
        }

        private static string TryConvertToBase64Honeywell1900(byte[] imageArray)
        {
            try
            {
                Console.WriteLine("Trying Honeywell 1900 format conversion...");
                int startIndex = FindImageStartIndex(imageArray);
                Console.WriteLine($"  Start index: {startIndex}");

                if (startIndex == -1)
                {
                    Console.WriteLine("  No start marker found, trying from beginning");
                    startIndex = 0;
                }

                // More flexible length check - just ensure we have some data after start
                if (startIndex >= imageArray.Length)
                {
                    Console.WriteLine("  Start index beyond array length");
                    return null;
                }

                // Try different approaches for extracting image data

                // Approach 1: Use original calculation but more flexible
                int remainingBytes = imageArray.Length - startIndex;
                Console.WriteLine($"  Remaining bytes after start: {remainingBytes}");
                Console.WriteLine($"  Required for 1900 format: {honeyWellPictureCmdLength + 2}");

                if (remainingBytes > honeyWellPictureCmdLength + 2)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength - 2))
                        {
                            Image img = Image.FromStream(ms);
                            using (MemoryStream msBase64 = new MemoryStream())
                            {
                                img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                                byte[] imgBytes = msBase64.ToArray();
                                Console.WriteLine("  ✓ Honeywell 1900 format succeeded with original calculation");
                                return Convert.ToBase64String(imgBytes);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  Original calculation failed: {ex.Message}");
                    }
                }

                // Approach 2: Try without command length subtraction
                try
                {
                    using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                    {
                        Image img = Image.FromStream(ms);
                        using (MemoryStream msBase64 = new MemoryStream())
                        {
                            img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                            byte[] imgBytes = msBase64.ToArray();
                            Console.WriteLine("  ✓ Honeywell 1900 format succeeded without command length subtraction");
                            return Convert.ToBase64String(imgBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Without command length subtraction failed: {ex.Message}");
                }

                Console.WriteLine("  ✗ Honeywell 1900 format failed");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting Honeywell 1900 to Base64: {ex.Message}");
                return null;
            }
        }

        private static string TryConvertToBase64Honeywell1950(byte[] imageArray)
        {
            try
            {
                Console.WriteLine("Trying Honeywell 1950 format conversion...");
                int startIndex = FindImageStartIndex(imageArray);
                Console.WriteLine($"  Start index: {startIndex}");

                if (startIndex == -1)
                {
                    Console.WriteLine("  No start marker found, trying from beginning");
                    startIndex = 0;
                }

                // More flexible length check
                if (startIndex >= imageArray.Length)
                {
                    Console.WriteLine("  Start index beyond array length");
                    return null;
                }

                // Try different approaches for extracting image data

                // Approach 1: Use original calculation but more flexible
                int remainingBytes = imageArray.Length - startIndex;
                Console.WriteLine($"  Remaining bytes after start: {remainingBytes}");
                Console.WriteLine($"  Required for 1950 format: {honeyWellPictureCmdLength_1950 + 2}");

                if (remainingBytes > honeyWellPictureCmdLength_1950 + 2)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength_1950 - 2))
                        {
                            Image img = Image.FromStream(ms);
                            using (MemoryStream msBase64 = new MemoryStream())
                            {
                                img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                                byte[] imgBytes = msBase64.ToArray();
                                Console.WriteLine("  ✓ Honeywell 1950 format succeeded with original calculation");
                                return Convert.ToBase64String(imgBytes);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  Original calculation failed: {ex.Message}");
                    }
                }

                // Approach 2: Try without command length subtraction
                try
                {
                    using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                    {
                        Image img = Image.FromStream(ms);
                        using (MemoryStream msBase64 = new MemoryStream())
                        {
                            img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                            byte[] imgBytes = msBase64.ToArray();
                            Console.WriteLine("  ✓ Honeywell 1950 format succeeded without command length subtraction");
                            return Convert.ToBase64String(imgBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Without command length subtraction failed: {ex.Message}");
                }

                Console.WriteLine("  ✗ Honeywell 1950 format failed");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting Honeywell 1950 to Base64: {ex.Message}");
                return null;
            }
        }

        private static string TryConvertToBase64Generic(byte[] imageArray)
        {
            try
            {
                Console.WriteLine("Trying generic format conversion...");

                // Approach 1: Try to parse as a direct image without command overhead
                try
                {
                    using (MemoryStream ms = new MemoryStream(imageArray))
                    {
                        Image img = Image.FromStream(ms);
                        using (MemoryStream msBase64 = new MemoryStream())
                        {
                            img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                            byte[] imgBytes = msBase64.ToArray();
                            Console.WriteLine("  ✓ Generic format succeeded with direct parsing");
                            return Convert.ToBase64String(imgBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Direct parsing failed: {ex.Message}");
                }

                // Approach 2: Try with image start index
                int startIndex = FindImageStartIndex(imageArray);
                Console.WriteLine($"  Start index: {startIndex}");

                if (startIndex >= 0 && startIndex < imageArray.Length)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                        {
                            Image img = Image.FromStream(ms);
                            using (MemoryStream msBase64 = new MemoryStream())
                            {
                                img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                                byte[] imgBytes = msBase64.ToArray();
                                Console.WriteLine("  ✓ Generic format succeeded with start index");
                                return Convert.ToBase64String(imgBytes);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  Start index parsing failed: {ex.Message}");
                    }
                }

                // Approach 3: Try to find JPEG/PNG headers and parse from there
                var jpegStart = FindJpegStart(imageArray);
                if (jpegStart >= 0)
                {
                    try
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, jpegStart, imageArray.Length - jpegStart))
                        {
                            Image img = Image.FromStream(ms);
                            using (MemoryStream msBase64 = new MemoryStream())
                            {
                                img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                                byte[] imgBytes = msBase64.ToArray();
                                Console.WriteLine("  ✓ Generic format succeeded with JPEG header detection");
                                return Convert.ToBase64String(imgBytes);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  JPEG header parsing failed: {ex.Message}");
                    }
                }

                Console.WriteLine("  ✗ Generic format failed");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting generic format to Base64: {ex.Message}");
                return null;
            }
        }

        private static int FindJpegStart(byte[] data)
        {
            // Look for JPEG header (0xFF 0xD8)
            for (int i = 0; i < data.Length - 1; i++)
            {
                if (data[i] == 0xFF && data[i + 1] == 0xD8)
                {
                    Console.WriteLine($"  Found JPEG header at index: {i}");
                    return i;
                }
            }

            // Look for PNG header (0x89 0x50 0x4E 0x47)
            for (int i = 0; i < data.Length - 3; i++)
            {
                if (data[i] == 0x89 && data[i + 1] == 0x50 && data[i + 2] == 0x4E && data[i + 3] == 0x47)
                {
                    Console.WriteLine($"  Found PNG header at index: {i}");
                    return i;
                }
            }

            return -1;
        }

        // Method to get the appropriate command for the current reader type
        public static string GetCaptureCommand(BarcodeReaderType readerType = BarcodeReaderType.Auto)
        {
            if (readerType == BarcodeReaderType.Auto)
                readerType = CurrentReaderType;

            switch (readerType)
            {
                case BarcodeReaderType.Honeywell1900:
                    return hwPictureCmd_1900; // استخدام الأمر المخصص للموديل 1900
                case BarcodeReaderType.Honeywell1950:
                    return hwPictureCmd_1950; // استخدام الأمر المخصص للموديل 1950
                default:
                    return hwPictureCmd; // الافتراضي (1950)
            }
        }

        // Method to detect barcode reader type from response data
        public static BarcodeReaderType DetectReaderType(byte[] responseData)
        {
            if (responseData == null || responseData.Length == 0)
                return BarcodeReaderType.Generic;

            Console.WriteLine($"Detecting barcode reader type from {responseData.Length} bytes of data");

            // Try to detect based on response patterns
            // This is a simple heuristic - you may need to adjust based on actual device responses

            // Check if it matches Honeywell 1950 pattern first (since current commands work with it)
            if (TryParseHoneywell1950Format(responseData) != null)
            {
                Console.WriteLine("Detected Honeywell 1950 format");
                return BarcodeReaderType.Honeywell1950;
            }

            // Check if it matches Honeywell 1900 pattern
            if (TryParseHoneywell1900Format(responseData) != null)
            {
                Console.WriteLine("Detected Honeywell 1900 format");
                return BarcodeReaderType.Honeywell1900;
            }

            Console.WriteLine("Using generic format");
            return BarcodeReaderType.Generic;
        }

        // Diagnostic method to help troubleshoot image data
        public static void DiagnoseImageData(byte[] imageData)
        {
            if (imageData == null || imageData.Length == 0)
            {
                Console.WriteLine("Image data is null or empty");
                return;
            }

            Console.WriteLine($"Image data diagnostics:");
            Console.WriteLine($"  Total length: {imageData.Length} bytes");
            Console.WriteLine($"  First 10 bytes: {string.Join(" ", imageData.Take(10).Select(b => $"0x{b:X2}"))}");
            Console.WriteLine($"  Last 10 bytes: {string.Join(" ", imageData.Skip(Math.Max(0, imageData.Length - 10)).Select(b => $"0x{b:X2}"))}");

            // Look for image start marker
            int startIndex = FindImageStartIndex(imageData);
            Console.WriteLine($"  Image start marker (0x1D) found at index: {startIndex}");

            // Check command lengths
            Console.WriteLine($"  Honeywell 1900 command length: {honeyWellPictureCmdLength}");
            Console.WriteLine($"  Honeywell 1950 command length: {honeyWellPictureCmdLength_1950}");

            // Check if data length is sufficient for each format
            if (startIndex >= 0)
            {
                int remainingBytes = imageData.Length - startIndex;
                Console.WriteLine($"  Bytes after start marker: {remainingBytes}");
                Console.WriteLine($"  Sufficient for 1900 format: {remainingBytes >= honeyWellPictureCmdLength + 2}");
                Console.WriteLine($"  Sufficient for 1950 format: {remainingBytes >= honeyWellPictureCmdLength_1950 + 2}");
            }
        }

    }

}
