﻿using System;
using System.Drawing;
using System.Text;
using System.IO;
using System.Collections.Generic;

namespace Mis.Shared.Interface
{
    public static class imgCapture
    {
        // Honeywell 1900 series commands
        const string hwImgsnpCmd_1900 = "IMGSNP2G1B1L";//أمر التصوير
        const string hwImgshpCmd_1900 = "IMGSHP2P0L843R639B0T0M8D1S6F";//أمر جلب الصورة من القارئ

        // Honeywell 1950 series commands (different format)
        const string hwImgsnpCmd_1950 = "IMGSNP2G1B1L";//أمر التصوير
        const string hwImgshpCmd_1950 = "IMGSHP8F75K26U";//أمر جلب الصورة من القارئ للموديل 1950

        // Default commands (for backward compatibility)
        public readonly static string hwPictureCmd = string.Format("{0};{1}.", hwImgsnpCmd_1900, hwImgshpCmd_1900);//جمع الأمرين مع بعض
        public readonly static string hwPictureCmd_1950 = string.Format("{0};{1}.", hwImgsnpCmd_1950, hwImgshpCmd_1950);//أمر للموديل 1950

        private readonly static int honeyWellPictureCmdLength = hwPictureCmd.Length; //طول الأمر
        private readonly static int honeyWellPictureCmdLength_1950 = hwPictureCmd_1950.Length; //طول الأمر للموديل 1950

        // Barcode reader types
        public enum BarcodeReaderType
        {
            Honeywell1900,
            Honeywell1950,
            Generic,
            Auto // Auto-detect
        }

        // Current barcode reader type (can be set dynamically)
        public static BarcodeReaderType CurrentReaderType { get; set; } = BarcodeReaderType.Auto;
        public static Image GetDeviceScannerImage(byte[] imageArray)
        {
            // Check if imageArray is null or empty
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null; // Not enough data for the image
            }

            // Attempt to create an image directly from the byte array
            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    Image img = Image.FromStream(ms);
                    Console.WriteLine("Image successfully created from byte array.");
                    return img; // Return the image if successful
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to image: {ex.Message}");
                return null; // Return null if there's an error
            }
        }



        public static Image GetbarcodeScannerImage(byte[] imageArray)
        {
            return GetbarcodeScannerImage(imageArray, CurrentReaderType);
        }

        public static Image GetbarcodeScannerImage(byte[] imageArray, BarcodeReaderType readerType)
        {
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null;
            }

            // Try different parsing methods based on reader type
            if (readerType == BarcodeReaderType.Auto)
            {
                // Try all known formats
                var image = TryParseHoneywell1900Format(imageArray);
                if (image != null) return image;

                image = TryParseHoneywell1950Format(imageArray);
                if (image != null) return image;

                image = TryParseGenericFormat(imageArray);
                if (image != null) return image;

                Console.WriteLine("Failed to parse image with any known format");
                return null;
            }
            else if (readerType == BarcodeReaderType.Honeywell1900)
            {
                return TryParseHoneywell1900Format(imageArray);
            }
            else if (readerType == BarcodeReaderType.Honeywell1950)
            {
                return TryParseHoneywell1950Format(imageArray);
            }
            else
            {
                return TryParseGenericFormat(imageArray);
            }
        }

        private static Image TryParseHoneywell1900Format(byte[] imageArray)
        {
            try
            {
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) return null;

                // Check if we have enough data for Honeywell 1900 format
                if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength + 2 > imageArray.Length)
                {
                    return null;
                }

                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength - 2))
                {
                    return Image.FromStream(ms);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing Honeywell 1900 format: {ex.Message}");
                return null;
            }
        }

        private static Image TryParseHoneywell1950Format(byte[] imageArray)
        {
            try
            {
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) return null;

                // Check if we have enough data for Honeywell 1950 format
                if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength_1950 + 2 > imageArray.Length)
                {
                    return null;
                }

                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength_1950 - 2))
                {
                    return Image.FromStream(ms);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing Honeywell 1950 format: {ex.Message}");
                return null;
            }
        }

        private static Image TryParseGenericFormat(byte[] imageArray)
        {
            try
            {
                // Try to parse as a direct image without command overhead
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    return Image.FromStream(ms);
                }
            }
            catch
            {
                try
                {
                    // Try with image start index
                    int startIndex = FindImageStartIndex(imageArray);
                    if (startIndex == -1) return null;

                    using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                    {
                        return Image.FromStream(ms);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing generic format: {ex.Message}");
                    return null;
                }
            }
        }

        private static int FindImageStartIndex(byte[] imageArray)
        {
            for (int i = 0; i < imageArray.Length; i++)
            {
                if (imageArray[i] == 0x1d) // Image data starts from byte value 0x1D
                {
                    return i + 1; // Increment to start from the next byte
                }
            }
            return 0; // If no start marker found, start from beginning
        }

        public static string GetImageAsBase64(byte[] imageArray)
        {
            return GetImageAsBase64(imageArray, CurrentReaderType);
        }

        public static string GetImageAsBase64(byte[] imageArray, BarcodeReaderType readerType)
        {
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null;
            }

            // Try different parsing methods based on reader type
            if (readerType == BarcodeReaderType.Auto)
            {
                // Try all known formats
                var base64 = TryConvertToBase64Honeywell1900(imageArray);
                if (base64 != null) return base64;

                base64 = TryConvertToBase64Honeywell1950(imageArray);
                if (base64 != null) return base64;

                base64 = TryConvertToBase64Generic(imageArray);
                if (base64 != null) return base64;

                Console.WriteLine("Failed to convert image to Base64 with any known format");
                return null;
            }
            else if (readerType == BarcodeReaderType.Honeywell1900)
            {
                return TryConvertToBase64Honeywell1900(imageArray);
            }
            else if (readerType == BarcodeReaderType.Honeywell1950)
            {
                return TryConvertToBase64Honeywell1950(imageArray);
            }
            else
            {
                return TryConvertToBase64Generic(imageArray);
            }
        }

        private static string TryConvertToBase64Honeywell1900(byte[] imageArray)
        {
            try
            {
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) return null;

                // Check if we have enough data for Honeywell 1900 format
                if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength + 2 > imageArray.Length)
                {
                    return null;
                }

                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength - 2))
                {
                    Image img = Image.FromStream(ms);
                    using (MemoryStream msBase64 = new MemoryStream())
                    {
                        img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                        byte[] imgBytes = msBase64.ToArray();
                        return Convert.ToBase64String(imgBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting Honeywell 1900 to Base64: {ex.Message}");
                return null;
            }
        }

        private static string TryConvertToBase64Honeywell1950(byte[] imageArray)
        {
            try
            {
                int startIndex = FindImageStartIndex(imageArray);
                if (startIndex == -1) return null;

                // Check if we have enough data for Honeywell 1950 format
                if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength_1950 + 2 > imageArray.Length)
                {
                    return null;
                }

                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength_1950 - 2))
                {
                    Image img = Image.FromStream(ms);
                    using (MemoryStream msBase64 = new MemoryStream())
                    {
                        img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                        byte[] imgBytes = msBase64.ToArray();
                        return Convert.ToBase64String(imgBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting Honeywell 1950 to Base64: {ex.Message}");
                return null;
            }
        }

        private static string TryConvertToBase64Generic(byte[] imageArray)
        {
            try
            {
                // Try to parse as a direct image without command overhead
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    Image img = Image.FromStream(ms);
                    using (MemoryStream msBase64 = new MemoryStream())
                    {
                        img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                        byte[] imgBytes = msBase64.ToArray();
                        return Convert.ToBase64String(imgBytes);
                    }
                }
            }
            catch
            {
                try
                {
                    // Try with image start index
                    int startIndex = FindImageStartIndex(imageArray);
                    if (startIndex == -1) return null;

                    using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex))
                    {
                        Image img = Image.FromStream(ms);
                        using (MemoryStream msBase64 = new MemoryStream())
                        {
                            img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                            byte[] imgBytes = msBase64.ToArray();
                            return Convert.ToBase64String(imgBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error converting generic format to Base64: {ex.Message}");
                    return null;
                }
            }
        }

        // Method to get the appropriate command for the current reader type
        public static string GetCaptureCommand(BarcodeReaderType readerType = BarcodeReaderType.Auto)
        {
            if (readerType == BarcodeReaderType.Auto)
                readerType = CurrentReaderType;

            switch (readerType)
            {
                case BarcodeReaderType.Honeywell1950:
                    return hwPictureCmd_1950;
                case BarcodeReaderType.Honeywell1900:
                default:
                    return hwPictureCmd;
            }
        }

        // Method to detect barcode reader type from response data
        public static BarcodeReaderType DetectReaderType(byte[] responseData)
        {
            if (responseData == null || responseData.Length == 0)
                return BarcodeReaderType.Generic;

            Console.WriteLine($"Detecting barcode reader type from {responseData.Length} bytes of data");

            // Try to detect based on response patterns
            // This is a simple heuristic - you may need to adjust based on actual device responses

            // Check if it matches Honeywell 1900 pattern
            if (TryParseHoneywell1900Format(responseData) != null)
            {
                Console.WriteLine("Detected Honeywell 1900 format");
                return BarcodeReaderType.Honeywell1900;
            }

            // Check if it matches Honeywell 1950 pattern
            if (TryParseHoneywell1950Format(responseData) != null)
            {
                Console.WriteLine("Detected Honeywell 1950 format");
                return BarcodeReaderType.Honeywell1950;
            }

            Console.WriteLine("Using generic format");
            return BarcodeReaderType.Generic;
        }

        // Diagnostic method to help troubleshoot image data
        public static void DiagnoseImageData(byte[] imageData)
        {
            if (imageData == null || imageData.Length == 0)
            {
                Console.WriteLine("Image data is null or empty");
                return;
            }

            Console.WriteLine($"Image data diagnostics:");
            Console.WriteLine($"  Total length: {imageData.Length} bytes");
            Console.WriteLine($"  First 10 bytes: {string.Join(" ", imageData.Take(10).Select(b => $"0x{b:X2}"))}");
            Console.WriteLine($"  Last 10 bytes: {string.Join(" ", imageData.Skip(Math.Max(0, imageData.Length - 10)).Select(b => $"0x{b:X2}"))}");

            // Look for image start marker
            int startIndex = FindImageStartIndex(imageData);
            Console.WriteLine($"  Image start marker (0x1D) found at index: {startIndex}");

            // Check command lengths
            Console.WriteLine($"  Honeywell 1900 command length: {honeyWellPictureCmdLength}");
            Console.WriteLine($"  Honeywell 1950 command length: {honeyWellPictureCmdLength_1950}");

            // Check if data length is sufficient for each format
            if (startIndex >= 0)
            {
                int remainingBytes = imageData.Length - startIndex;
                Console.WriteLine($"  Bytes after start marker: {remainingBytes}");
                Console.WriteLine($"  Sufficient for 1900 format: {remainingBytes >= honeyWellPictureCmdLength + 2}");
                Console.WriteLine($"  Sufficient for 1950 format: {remainingBytes >= honeyWellPictureCmdLength_1950 + 2}");
            }
        }

    }

}
